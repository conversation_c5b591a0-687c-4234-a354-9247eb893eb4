# CloudAudit Frontend Environment Variables
# Copy this file to .env and modify the values as needed for your environment
# All values can be overridden via deployment configuration or Docker environment variables

# =============================================================================
# API Configuration
# =============================================================================

# Backend API Base URL
VITE_API_BASE_URL=http://localhost:8000

# API Timeout (in milliseconds)
VITE_API_TIMEOUT=30000

# API Retry Attempts
VITE_API_MAX_RETRIES=3

# =============================================================================
# Application Configuration
# =============================================================================

# Application Environment (development, staging, production)
VITE_APP_ENV=development

# Application Name
VITE_APP_NAME=CloudAudit

# Application Version (automatically set during build)
VITE_APP_VERSION=1.0.0

# Application Base URL (for routing)
VITE_APP_BASE_URL=/

# =============================================================================
# Security Configuration
# =============================================================================

# Enable/Disable Security Headers
VITE_ENABLE_SECURITY_HEADERS=true

# Content Security Policy Mode (report-only, enforce)
VITE_CSP_MODE=enforce

# Enable/Disable HTTPS Redirect
VITE_FORCE_HTTPS=false

# Session Timeout (in minutes)
VITE_SESSION_TIMEOUT=60

# =============================================================================
# Feature Flags
# =============================================================================

# Enable React Query DevTools
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true

# Enable Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=false

# Enable Error Reporting
VITE_ENABLE_ERROR_REPORTING=false

# Enable Analytics
VITE_ENABLE_ANALYTICS=false

# =============================================================================
# Logging Configuration
# =============================================================================

# Log Level (debug, info, warn, error)
VITE_LOG_LEVEL=info

# Enable Console Logging
VITE_ENABLE_CONSOLE_LOGGING=true

# Enable Remote Logging
VITE_ENABLE_REMOTE_LOGGING=false

# Remote Logging Endpoint
VITE_REMOTE_LOG_ENDPOINT=

# =============================================================================
# Performance Configuration
# =============================================================================

# Enable Service Worker
VITE_ENABLE_SERVICE_WORKER=false

# Cache Strategy (cache-first, network-first, stale-while-revalidate)
VITE_CACHE_STRATEGY=stale-while-revalidate

# Cache Duration (in seconds)
VITE_CACHE_DURATION=3600

# =============================================================================
# Third-Party Services
# =============================================================================

# Sentry DSN (for error reporting)
VITE_SENTRY_DSN=

# Google Analytics ID
VITE_GA_TRACKING_ID=

# Hotjar Site ID
VITE_HOTJAR_SITE_ID=

# =============================================================================
# Build Configuration
# =============================================================================

# Build Target (development, production)
VITE_BUILD_TARGET=development

# Enable Source Maps
VITE_ENABLE_SOURCE_MAPS=true

# Bundle Analyzer
VITE_ENABLE_BUNDLE_ANALYZER=false

# =============================================================================
# Development Configuration
# =============================================================================

# Development Server Port
VITE_DEV_PORT=5173

# Development Server Host
VITE_DEV_HOST=0.0.0.0

# Enable Hot Module Replacement
VITE_ENABLE_HMR=true

# Enable HTTPS in Development
VITE_DEV_HTTPS=false

# =============================================================================
# Deployment Configuration
# =============================================================================

# Deployment Platform (vercel, netlify, aws, docker)
VITE_DEPLOYMENT_PLATFORM=

# CDN Base URL
VITE_CDN_BASE_URL=

# Asset Base URL
VITE_ASSET_BASE_URL=

# =============================================================================
# Monitoring & Observability
# =============================================================================

# Health Check Endpoint
VITE_HEALTH_CHECK_ENDPOINT=/health

# Metrics Collection Interval (in seconds)
VITE_METRICS_INTERVAL=60

# Enable Real User Monitoring
VITE_ENABLE_RUM=false

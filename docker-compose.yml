# CloudAudit Frontend - Docker Compose Configuration

version: "3.8"

services:
  # Development service
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cloudaudit-frontend-dev
    ports:
      - "5173:5173"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      # Override default .env values for development
      - VITE_APP_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
      - VITE_LOG_LEVEL=debug
      - VITE_ENABLE_CONSOLE_LOGGING=true
      - VITE_ENABLE_ERROR_REPORTING=false
    networks:
      - cloudaudit-network
    profiles:
      - development

  # Production service
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cloudaudit-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      # Override default .env values for production
      - VITE_APP_ENV=production
      - VITE_API_BASE_URL=http://*************:8000
      - VITE_ENABLE_SECURITY_HEADERS=true
      - VITE_CSP_MODE=enforce
      - VITE_FORCE_HTTPS=true
      - VITE_SESSION_TIMEOUT=60
      - VITE_ENABLE_REACT_QUERY_DEVTOOLS=false
      - VITE_ENABLE_ERROR_REPORTING=true
      - VITE_ENABLE_ANALYTICS=true
      - VITE_LOG_LEVEL=error
      - VITE_ENABLE_CONSOLE_LOGGING=false
      - VITE_ENABLE_REMOTE_LOGGING=true
      - VITE_ENABLE_SERVICE_WORKER=true
      - VITE_CACHE_STRATEGY=stale-while-revalidate
      - VITE_CACHE_DURATION=3600
      - VITE_BUILD_TARGET=production
      - VITE_ENABLE_SOURCE_MAPS=false
      - VITE_METRICS_INTERVAL=60
      - VITE_ENABLE_RUM=true
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - cloudaudit-network
    profiles:
      - production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Staging service
  frontend-staging:
    build:
      context: .
      dockerfile: Dockerfile
      target: staging
    container_name: cloudaudit-frontend-staging
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
      # Override default .env values for staging
      - VITE_APP_ENV=staging
      - VITE_API_BASE_URL=https://staging-api.cloudaudit.com
      - VITE_APP_NAME=CloudAudit (Staging)
      - VITE_APP_VERSION=1.0.0-staging
      - VITE_ENABLE_SECURITY_HEADERS=true
      - VITE_CSP_MODE=enforce
      - VITE_FORCE_HTTPS=true
      - VITE_SESSION_TIMEOUT=60
      - VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
      - VITE_ENABLE_ERROR_REPORTING=true
      - VITE_ENABLE_ANALYTICS=false
      - VITE_LOG_LEVEL=warn
      - VITE_ENABLE_CONSOLE_LOGGING=true
      - VITE_ENABLE_REMOTE_LOGGING=true
      - VITE_REMOTE_LOG_ENDPOINT=https://staging-logs.cloudaudit.com/api/logs
      - VITE_ENABLE_SERVICE_WORKER=true
      - VITE_CACHE_STRATEGY=network-first
      - VITE_CACHE_DURATION=1800
      - VITE_BUILD_TARGET=staging
      - VITE_ENABLE_SOURCE_MAPS=true
      - VITE_CDN_BASE_URL=https://staging-cdn.cloudaudit.com
      - VITE_ASSET_BASE_URL=https://staging-assets.cloudaudit.com
      - VITE_METRICS_INTERVAL=60
      - VITE_ENABLE_RUM=true
    networks:
      - cloudaudit-network
    profiles:
      - staging
    restart: unless-stopped

  # Nginx reverse proxy (for production)
  nginx-proxy:
    image: nginx:alpine
    container_name: cloudaudit-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - frontend-prod
    networks:
      - cloudaudit-network
    profiles:
      - production-proxy
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:alpine
    container_name: cloudaudit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - cloudaudit-network
    profiles:
      - production
      - staging
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  cloudaudit-network:
    driver: bridge
    name: cloudaudit-network

volumes:
  nginx-cache:
    name: cloudaudit-nginx-cache
  redis-data:
    name: cloudaudit-redis-data

import React from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useUserStore } from '../stores/userStore';
import {
    createUser,
    deleteUser,
    createCustomRole,
    updateCustomRole,
    deleteCustomRole,
    assignRole,
    revokeRole,
    getRoles,
    getUserPermissions,
    getTeamMembers,
    updateTeamMember,
    changePassword,
} from '@services/user';
import { getUserInfo } from '@services/auth';
import { ServiceError } from '@services/baseService';
import type {
    CreateUserRequest,
    CreateUserResponse,
    CreateCustomRoleRequest,
    AssignRoleRequest,
    AssignRoleResponse,
    RevokeRoleRequest,
    RevokeRoleResponse,
    UpdateCustomRoleRequest,
    Role,
    Permission,
    CreateCustomRoleResponse,
    UpdateCustomRoleResponse,
    DeleteCustomRoleResponse,
    DeleteUserResponse,
    UserInfoResponse,
    UserPermissionsResponse,
    TeamMembersResponse,
    TeamMember,
    RolesResponse,
    UpdateTeamMemberRequest,
    UpdateTeamMemberResponse,
    ChangePasswordRequest,
    ChangePasswordResponse
} from '@/types/api';

// Query keys as objects following TanStack Query conventions
const QUERY_KEYS = {
    roles: ['roles'] as const,
    permissions: ['permissions'] as const,
    users: ['users'] as const,
    teamMembers: ['team-members'] as const
};

/**
 * Hook to fetch roles data with proper separation of concerns
 * @returns Query result with roles data
 */
export const useRoles = () => {
    const { setRoles } = useUserStore();

    const result = useQuery<RolesResponse, ServiceError, { roles: Role[], customRoles: Role[] }>({
        queryKey: QUERY_KEYS.roles,
        queryFn: async () => {
            try {
                const response = await getRoles();
                return response;
            } catch (error) {
                // Convert to ServiceError if it's not already
                if (!(error instanceof ServiceError)) {
                    throw new ServiceError(
                        'UNKNOWN_ERROR' as any, // Type assertion to fix the error temporarily
                        error instanceof Error ? error.message : 'Failed to fetch roles',
                        error
                    );
                }
                throw error;
            }
        },
        select: (data) => ({
            roles: data.data.roles,
            customRoles: data.data.custom_roles
        }),
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    });

    // Update the Zustand store when data changes
    React.useEffect(() => {
        if (result.data) {
            setRoles(result.data.roles, result.data.customRoles);
        }
    }, [result.data, setRoles]);

    return result;
};

/**
 * Hook to fetch user permissions with proper separation of concerns
 * @returns Query result with permissions data
 */
export const usePermissions = () => {
    const { setPermissions } = useUserStore();

    const result = useQuery<UserPermissionsResponse, ServiceError, Permission[]>({
        queryKey: QUERY_KEYS.permissions,
        queryFn: async () => {
            try {
                const response = await getUserPermissions();
                return response;
            } catch (error) {
                // Convert to ServiceError if it's not already
                if (!(error instanceof ServiceError)) {
                    throw new ServiceError(
                        'UNKNOWN_ERROR' as any,
                        error instanceof Error ? error.message : 'Failed to fetch permissions',
                        error
                    );
                }
                throw error;
            }
        },
        select: (data) => data.data,
        staleTime: 1 * 60 * 1000, // 5 minutes
        retry: 2,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });

    // Update the Zustand store when data changes
    React.useEffect(() => {
        if (result.data) {
            setPermissions(result.data);
        }
    }, [result.data, setPermissions]);

    return result;
};

/**
 * Hook to fetch user information
 * @returns Query result with user data
 */
export const useUsers = () => {
    return useQuery<UserInfoResponse, ServiceError>({
        queryKey: QUERY_KEYS.users,
        queryFn: async () => {
            try {
                const response = await getUserInfo();
                return response;
            } catch (error) {
                // Convert to ServiceError if it's not already
                if (!(error instanceof ServiceError)) {
                    throw new ServiceError(
                        'UNKNOWN_ERROR' as any,
                        error instanceof Error ? error.message : 'Failed to fetch user info',
                        error
                    );
                }
                throw error;
            }
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
    });
};

// Mutation hooks
/**
 * Hook for creating a new user
 * @returns Mutation result for creating a user
 */
export const useCreateUser = () => {
    const queryClient = useQueryClient();

    return useMutation<CreateUserResponse, Error, CreateUserRequest, { previousTeamMembers?: TeamMembersResponse }>({
        mutationFn: (data: CreateUserRequest) => createUser(data),

        onMutate: async (_variables) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.teamMembers });

            // Snapshot the previous value
            const previousTeamMembers = queryClient.getQueryData<TeamMembersResponse>(QUERY_KEYS.teamMembers);

            // Return a context object with the snapshot
            return { previousTeamMembers };
        },

        onSuccess: () => {
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.teamMembers });
        },

        onError: (error, _variables, context) => {
            // Revert to the previous state if available
            if (context?.previousTeamMembers) {
                queryClient.setQueryData(QUERY_KEYS.teamMembers, context.previousTeamMembers);
            }

            // Return the error for handling in the component
            return error;
        }
    });
};

/**
 * Hook for deleting a user
 * @returns Mutation result for deleting a user
 */
export const useDeleteUser = () => {
    const queryClient = useQueryClient();

    return useMutation<DeleteUserResponse, Error, number, { previousTeamMembers?: TeamMembersResponse, userId?: number }>({
        mutationFn: (userId: number) => deleteUser(userId),

        onMutate: async (userId) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.teamMembers });

            // Snapshot the previous value
            const previousTeamMembers = queryClient.getQueryData<TeamMembersResponse>(QUERY_KEYS.teamMembers);

            // Optimistically update the UI by removing the deleted user
            if (previousTeamMembers && Array.isArray(previousTeamMembers.data)) {
                const updatedMembers = {
                    ...previousTeamMembers,
                    data: previousTeamMembers.data.filter(member => member.id !== userId)
                };

                queryClient.setQueryData<TeamMembersResponse>(
                    QUERY_KEYS.teamMembers,
                    updatedMembers
                );
            }

            // Return a context object with the snapshot and userId
            return { previousTeamMembers, userId };
        },

        onSuccess: () => {
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.teamMembers });
        },

        onError: (error, _userId, context) => {
            // Revert to the previous state if available
            if (context?.previousTeamMembers) {
                queryClient.setQueryData(QUERY_KEYS.teamMembers, context.previousTeamMembers);
            }

            // Return the error for handling in the component
            return error;
        }
    });
};

/**
 * Hook for creating a custom role
 * @returns Mutation result for creating a custom role
 */
export const useCreateCustomRole = () => {
    const queryClient = useQueryClient();
    const {
        addCustomRole,
        removeCustomRole,
        setRoleCreating,
        setError
    } = useUserStore();

    return useMutation<CreateCustomRoleResponse, Error, CreateCustomRoleRequest, { previousRoles?: RolesResponse, tempId?: number }>({
        mutationFn: (data: CreateCustomRoleRequest) => createCustomRole(data),

        onMutate: async (variables) => {
            // Set creating state to true
            setRoleCreating(true);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.roles });

            // Snapshot the previous value
            const previousRoles = queryClient.getQueryData<RolesResponse>(QUERY_KEYS.roles);

            // Create a temporary ID for optimistic update
            const tempId = Date.now();

            // Optimistically update the store
            const newRole: Role = {
                id: tempId, // Temporary ID that will be updated when the query is refetched
                name: variables.name,
                permissions: variables.permissions
            };

            addCustomRole(newRole);

            // Return a context object with the snapshot
            return { previousRoles, tempId };
        },

        onSuccess: (_response, _variables, _context) => {
            // Invalidate and refetch
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.roles });

            // Clear creating state
            setRoleCreating(false);
        },

        onError: (error, _variables, context) => {
            // Revert to the previous state if available
            if (context?.previousRoles) {
                queryClient.setQueryData(QUERY_KEYS.roles, context.previousRoles);
            }

            // Remove the optimistically added role
            if (context?.tempId) {
                removeCustomRole(context.tempId);
            }

            // Set error state
            setError(error.message || 'Failed to create custom role');

            // Clear creating state
            setRoleCreating(false);
        },

        onSettled: () => {
            // Always ensure creating state is reset
            setRoleCreating(false);
        }
    });
};

/**
 * Hook for updating a custom role
 * @returns Mutation result for updating a custom role
 */
export const useUpdateCustomRole = () => {
    const queryClient = useQueryClient();
    const {
        updateCustomRole: updateRole,
        setRoleUpdating,
        setError
    } = useUserStore();

    return useMutation<UpdateCustomRoleResponse, Error, UpdateCustomRoleRequest, { previousRoles?: RolesResponse, roleId?: number }>({
        mutationFn: (data: UpdateCustomRoleRequest) => updateCustomRole(data),

        onMutate: async (variables) => {
            // Set updating state for this role
            setRoleUpdating(variables.id, true);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.roles });

            // Snapshot the previous value
            const previousRoles = queryClient.getQueryData<RolesResponse>(QUERY_KEYS.roles);

            // Optimistically update the store
            const updatedRole: Role = {
                id: variables.id,
                name: variables.name,
                permissions: variables.permissions
            };

            updateRole(updatedRole);

            // Return a context object with the snapshot and role ID
            return { previousRoles, roleId: variables.id };
        },

        onSuccess: (_response, variables) => {
            // Invalidate and refetch
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.roles });

            // Clear updating state for this role
            setRoleUpdating(variables.id, false);
        },

        onError: (error, variables, context) => {
            // Revert to the previous state if available
            if (context?.previousRoles) {
                queryClient.setQueryData(QUERY_KEYS.roles, context.previousRoles);
            }

            // Set error state
            setError(error.message || `Failed to update role: ${variables.name}`);

            // Clear updating state for this role
            if (context?.roleId) {
                setRoleUpdating(context.roleId, false);
            }
        },

        onSettled: (_data, _error, variables) => {
            // Always ensure updating state is reset
            setRoleUpdating(variables.id, false);
        }
    });
};

/**
 * Hook for deleting a custom role
 * @returns Mutation result for deleting a custom role
 */
export const useDeleteCustomRole = () => {
    const queryClient = useQueryClient();
    const {
        removeCustomRole,
        addCustomRole,
        setRoleDeleting,
        setError
    } = useUserStore();

    return useMutation<DeleteCustomRoleResponse, Error, number, { previousRoles?: RolesResponse, roleToDelete?: Role, roleId?: number }>({
        mutationFn: (roleId: number) => deleteCustomRole(roleId),

        onMutate: async (roleId) => {
            // Set deleting state for this role
            setRoleDeleting(roleId, true);

            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.roles });

            // Snapshot the previous value
            const previousRoles = queryClient.getQueryData<RolesResponse>(QUERY_KEYS.roles);

            // Get the role before removing it (for potential rollback)
            const roleToDelete = previousRoles?.data.custom_roles.find(r => r.id === roleId);

            // Optimistically update the store
            removeCustomRole(roleId);

            // Return a context object with the snapshot and deleted role
            return { previousRoles, roleToDelete, roleId };
        },

        onSuccess: (_response, roleId) => {
            // Invalidate and refetch
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.roles });

            // Clear deleting state for this role
            setRoleDeleting(roleId, false);
        },

        onError: (error, roleId, context) => {
            // Revert to the previous state if available
            if (context?.previousRoles) {
                queryClient.setQueryData(QUERY_KEYS.roles, context.previousRoles);
            }

            // Add back the deleted role if we have it
            if (context?.roleToDelete) {
                addCustomRole(context.roleToDelete);
            }

            // Set error state
            setError(error.message || `Failed to delete role with ID: ${roleId}`);

            // Clear deleting state for this role
            if (context?.roleId) {
                setRoleDeleting(context.roleId, false);
            }
        },

        onSettled: (_data, _error, roleId) => {
            // Always ensure deleting state is reset
            setRoleDeleting(roleId, false);
        }
    });
};

/**
 * Hook for assigning a role to a user
 * @returns Mutation result for assigning a role
 */
export const useAssignRole = () => {
    const queryClient = useQueryClient();

    return useMutation<AssignRoleResponse, Error, AssignRoleRequest>({
        mutationFn: (data: AssignRoleRequest) => assignRole(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.roles });
        }
    });
};

/**
 * Hook for revoking a role from a user
 * @returns Mutation result for revoking a role
 */
export const useRevokeRole = () => {
    const queryClient = useQueryClient();

    return useMutation<RevokeRoleResponse, Error, RevokeRoleRequest>({
        mutationFn: (data: RevokeRoleRequest) => revokeRole(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.roles });
        }
    });
};

/**
 * Hook to fetch team members data
 * @returns Query result with team members data
 */
export const useTeamMembers = () => {
    return useQuery<TeamMembersResponse, ServiceError, TeamMember[]>({
        queryKey: QUERY_KEYS.teamMembers,
        queryFn: async () => {
            try {
                const response = await getTeamMembers();
                return response;
            } catch (error) {
                // Convert to ServiceError if it's not already
                if (!(error instanceof ServiceError)) {
                    throw new ServiceError(
                        'UNKNOWN_ERROR' as any,
                        error instanceof Error ? error.message : 'Failed to fetch team members',
                        error
                    );
                }
                throw error;
            }
        },
        select: (data) => {
            // Ensure we're returning the array of team members
            return Array.isArray(data.data) ? data.data : [];
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
    });
};

/**
 * Hook for updating a team member's role
 * @returns Mutation result for updating a team member's role
 */
export const useUpdateTeamMember = () => {
    const queryClient = useQueryClient();

    return useMutation<UpdateTeamMemberResponse, Error, UpdateTeamMemberRequest, { previousTeamMembers?: TeamMembersResponse }>({
        mutationFn: (data: UpdateTeamMemberRequest) => updateTeamMember(data),

        onMutate: async (_variables) => {
            // Cancel any outgoing refetches
            await queryClient.cancelQueries({ queryKey: QUERY_KEYS.teamMembers });

            // Snapshot the previous value
            const previousTeamMembers = queryClient.getQueryData<TeamMembersResponse>(QUERY_KEYS.teamMembers);

            // Return a context object with the snapshot
            return { previousTeamMembers };
        },

        onSuccess: () => {
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: QUERY_KEYS.teamMembers });
        },

        onError: (error, _variables, context) => {
            // Revert to the previous state if available
            if (context?.previousTeamMembers) {
                queryClient.setQueryData(QUERY_KEYS.teamMembers, context.previousTeamMembers);
            }

            // Return the error for handling in the component
            return error;
        }
    });
};

/**
 * Hook for changing a user's password
 * @returns Mutation result for changing password
 */
export const useChangePassword = () => {
    return useMutation<ChangePasswordResponse, Error, ChangePasswordRequest>({
        mutationFn: (data: ChangePasswordRequest) => changePassword(data),

        onSuccess: () => {
            // No need to invalidate queries as password change doesn't affect displayed data
            // Success handling will be done in the component
        },

        onError: (error) => {
            // Error handling will be done in the component
            return error;
        }
    });
};
# Netlify Configuration for CloudAudit Frontend

[build]
  # Build command
  command = "npm run build"

  # Output directory
  publish = "dist"

  # Environment variables for build
  environment = { VITE_APP_ENV = "production", VITE_BUILD_TARGET = "production" }

[build.processing]
  # Skip processing for already optimized files
  skip_processing = false

[build.processing.css]
  # CSS processing
  bundle = true
  minify = true

[build.processing.js]
  # JavaScript processing
  bundle = true
  minify = true

[build.processing.html]
  # HTML processing
  pretty_urls = true

[build.processing.images]
  # Image processing
  compress = true

# Redirects and rewrites
[[redirects]]
  # SPA fallback
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin", "user"]}

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/logo.svg"
  [headers.values]
    Cache-Control = "public, max-age=604800"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/robots.txt"
  [headers.values]
    Cache-Control = "public, max-age=86400"

# Environment-specific settings
[context.production]
  command = "npm run build"
  environment = {
    VITE_APP_ENV = "production",
    VITE_ENABLE_SECURITY_HEADERS = "true",
    VITE_CSP_MODE = "enforce",
    VITE_FORCE_HTTPS = "true",
    VITE_SESSION_TIMEOUT = "60",
    VITE_ENABLE_REACT_QUERY_DEVTOOLS = "false",
    VITE_ENABLE_ERROR_REPORTING = "true",
    VITE_ENABLE_ANALYTICS = "true",
    VITE_LOG_LEVEL = "error",
    VITE_ENABLE_CONSOLE_LOGGING = "false",
    VITE_ENABLE_REMOTE_LOGGING = "true",
    VITE_ENABLE_SERVICE_WORKER = "true",
    VITE_CACHE_STRATEGY = "stale-while-revalidate",
    VITE_CACHE_DURATION = "3600",
    VITE_BUILD_TARGET = "production",
    VITE_ENABLE_SOURCE_MAPS = "false",
    VITE_METRICS_INTERVAL = "60",
    VITE_ENABLE_RUM = "true"
  }

[context.staging]
  command = "npm run build"
  environment = {
    VITE_APP_ENV = "staging",
    VITE_APP_NAME = "CloudAudit (Staging)",
    VITE_APP_VERSION = "1.0.0-staging",
    VITE_ENABLE_SECURITY_HEADERS = "true",
    VITE_CSP_MODE = "enforce",
    VITE_FORCE_HTTPS = "true",
    VITE_SESSION_TIMEOUT = "60",
    VITE_ENABLE_REACT_QUERY_DEVTOOLS = "true",
    VITE_ENABLE_ERROR_REPORTING = "true",
    VITE_ENABLE_ANALYTICS = "false",
    VITE_LOG_LEVEL = "warn",
    VITE_ENABLE_CONSOLE_LOGGING = "true",
    VITE_ENABLE_REMOTE_LOGGING = "true",
    VITE_ENABLE_SERVICE_WORKER = "true",
    VITE_CACHE_STRATEGY = "network-first",
    VITE_CACHE_DURATION = "1800",
    VITE_BUILD_TARGET = "staging",
    VITE_ENABLE_SOURCE_MAPS = "true",
    VITE_METRICS_INTERVAL = "60",
    VITE_ENABLE_RUM = "true"
  }

[context.deploy-preview]
  command = "npm run build"
  environment = { VITE_APP_ENV = "staging" }

[context.branch-deploy]
  command = "npm run build"
  environment = { VITE_APP_ENV = "development" }

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"

  [plugins.inputs.thresholds]
    performance = 0.9
    accessibility = 0.9
    best-practices = 0.9
    seo = 0.9

[[plugins]]
  package = "netlify-plugin-submit-sitemap"

  [plugins.inputs]
    baseUrl = "https://cloudaudit.com"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing"
    ]

# Edge functions (if needed)
[[edge_functions]]
  function = "security-headers"
  path = "/*"

# Forms (if needed for contact forms)
[forms]
  spam_protection = true

# Functions (if needed for serverless functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

{"name": "cloud-security-scanner", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"No tests specified\" && exit 0", "test:coverage": "echo \"No tests specified\" && exit 0", "validate": "npm run type-check && npm run lint", "prepare": "npm run validate", "start": "npm run dev", "serve": "npm run preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@sentry/react": "^9.22.0", "@tanstack/react-query": "^5.74.11", "@tanstack/react-query-devtools": "^5.74.11", "axios": "^1.9.0", "clsx": "^2.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.2.1", "web-vitals": "^5.0.1", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.21", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "compression": "^1.8.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.4", "vite-bundle-analyzer": "^0.21.0"}}
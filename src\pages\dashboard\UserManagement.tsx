import {
  useCreateUser,
  useDeleteUser,
  useR<PERSON>s,
  useTeamMembers,
  useUpdateTeamMember,
  useUsers,
  useChangePassword,
} from "@/hooks/useUserManagement";
import { formatDate } from "@/lib/dateUtils";
import {
  createUserSchema,
  editUserSchema,
  changePasswordSchema,
} from "@/schemas/userManagement";
import type { Role } from "@/types/api";
import { RoleManagement } from "@components/RoleManagement";
import Badge from "@components/ui/Badge";
import Button from "@components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import Input from "@components/ui/Input";
import Modal from "@components/ui/Modal";
import { Skeleton } from "@components/ui/Skeleton";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  Edit,
  Key,
  Mail,
  Plus,
  Trash2,
  User,
  UserPlus,
} from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

// Delete confirmation modal component
interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userName: string;
  isLoading: boolean;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  userName,
  isLoading,
}) => {
  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Confirm Deletion">
      <div className="p-6">
        <div className="flex items-start mb-4">
          <div className="mr-3 mt-0.5">
            <AlertCircle className="h-6 w-6 text-red-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-dark-100">
              Delete Team Member: {userName}
            </h3>
            <p className="text-dark-400 mt-1">
              Are you sure you want to delete this team member? This action
              cannot be undone. The user will lose access to your workspace.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant="error"
            onClick={onConfirm}
            isLoading={isLoading}
            leftIcon={<Trash2 size={16} />}
          >
            Delete Team Member
          </Button>
        </div>
      </div>
    </Modal>
  );
};

interface User {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  roles: Role[];
  custom_roles: Role[];
  status: "Active" | "Pending" | "Suspended";
  workspace_name: string;
  is_owner?: boolean;
  role_name?: string;
  is_custom_role?: boolean;
  created_at?: string;
}

const UserManagement: React.FC = () => {
  // State for modals
  const [newUserModalOpen, setNewUserModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [changePasswordModalOpen, setChangePasswordModalOpen] = useState(false);
  const [userToChangePassword, setUserToChangePassword] = useState<User | null>(
    null
  );

  // Fetch data using React Query hooks
  const createUserMutation = useCreateUser();
  const deleteUserMutation = useDeleteUser();
  const updateTeamMemberMutation = useUpdateTeamMember();
  const changePasswordMutation = useChangePassword();
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();
  const { allPermissions: permissions, isLoading: isLoadingPermissions } =
    usePermissionContext();
  const { data: usersData, isLoading: isLoadingUsers } = useUsers();
  const {
    data: teamMembers,
    isLoading: isLoadingTeamMembers,
    error: teamMembersError,
  } = useTeamMembers();

  // Extract roles and permissions from React Query data
  const roles = rolesData?.roles || [];
  const customRoles = rolesData?.customRoles || [];

  // Create user form with Zod validation
  const {
    register: registerCreateUser,
    handleSubmit: handleSubmitCreateUser,
    formState: { errors: createUserErrors },
    reset: resetCreateUserForm,
    setValue: setCreateUserValue,
    clearErrors: clearCreateUserErrors,
  } = useForm({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      password: "",
      role: null,
    },
  });

  // Edit user form with Zod validation
  const {
    register: registerEditUser,
    handleSubmit: handleSubmitEditUser,
    formState: { errors: editUserErrors },
    setValue: setEditUserValue,
    clearErrors: clearEditUserErrors,
  } = useForm({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      role: null,
    },
  });

  // Change password form with Zod validation
  const {
    register: registerChangePassword,
    handleSubmit: handleSubmitChangePassword,
    formState: { errors: changePasswordErrors },
    reset: resetChangePasswordForm,
  } = useForm({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      password: "",
      confirm_password: "",
    },
  });

  // Reset form when modal closes
  const resetAddUserForm = () => {
    resetCreateUserForm();
  };

  // Helper function to get the user's role based on role_name and is_custom_role
  const getUserRole = (
    user: User,
    allRoles: Role[],
    allCustomRoles: Role[]
  ): Role | undefined => {
    if (!user.role_name) return undefined;

    if (user.is_custom_role) {
      return allCustomRoles.find((r) => r.name === user.role_name);
    } else {
      return allRoles.find((r) => r.name === user.role_name);
    }
  };

  // Set up edit user form when editing user changes
  React.useEffect(() => {
    if (editingUser) {
      // Pre-populate first_name and last_name if available, otherwise use empty strings
      setEditUserValue("first_name", editingUser.first_name || "");
      setEditUserValue("last_name", editingUser.last_name || "");
      setEditUserValue("email", editingUser.email);

      // Find the role for this user based on role_name and is_custom_role
      const userRole = getUserRole(editingUser, roles, customRoles);

      if (userRole) {
        // Use setValue with any type to bypass TypeScript checking
        (setEditUserValue as any)("role", userRole);
      }
    }
  }, [editingUser, roles, customRoles, setEditUserValue]);

  // Helper function to get the current role ID for the select dropdown
  const getCurrentRoleId = (user: User | null): string => {
    if (!user) return "";
    const userRole = getUserRole(user, roles, customRoles);
    return userRole ? userRole.id.toString() : "";
  };

  // Handle adding a new user
  const onSubmitCreateUser = async (data: any) => {
    try {
      // Ensure we have a valid role
      if (!data.role || !data.role.id) {
        toast.error("Please select a valid role");
        return;
      }

      // Check if the selected role is a custom role
      const isCustomRole = customRoles.some(
        (role: Role) => role.id === data.role.id
      );

      // Show loading toast while creating user
      const loadingToast = toast.loading("Adding team member...");

      await createUserMutation.mutateAsync({
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        password: data.password,
        role_id: data.role.id,
        is_custom_role: isCustomRole,
      });

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Team member added successfully");
      resetCreateUserForm();
      setNewUserModalOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to add team member";

      toast.error(errorMessage);
    }
  };

  // Handle opening the delete confirmation modal
  const handleDeleteClick = (user: User) => {
    setUserToDelete(user);
    setDeleteModalOpen(true);
  };

  // Handle deleting a user
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      // Show loading toast while deleting user
      const loadingToast = toast.loading("Deleting team member...");

      await deleteUserMutation.mutateAsync(userToDelete.id);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Team member deleted successfully");
      setDeleteModalOpen(false);
      setUserToDelete(null);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete team member";

      toast.error(errorMessage);
    }
  };

  // Handle opening the change password modal
  const handleChangePasswordClick = (user: User) => {
    setUserToChangePassword(user);
    setChangePasswordModalOpen(true);
    resetChangePasswordForm();
  };

  // Handle changing password
  const handleChangePassword = async (data: any) => {
    if (!userToChangePassword) return;

    try {
      // Show loading toast while changing password
      const loadingToast = toast.loading("Changing password...");

      await changePasswordMutation.mutateAsync({
        user_id: userToChangePassword.id,
        password: data.password,
        confirm_password: data.confirm_password,
      });

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Password changed successfully");
      setChangePasswordModalOpen(false);
      setUserToChangePassword(null);
      resetChangePasswordForm();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to change password";

      toast.error(errorMessage);
    }
  };

  // Render skeleton UI components for loading states
  const renderTeamMembersTableSkeleton = () => (
    <Card>
      <CardHeader>
        <CardTitle>Team Members</CardTitle>
        <CardDescription>
          All members with access to your workspace
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-dark-800">
                <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                  User
                </th>
                <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                  Role
                </th>
                <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                  Workspace Owner
                </th>
                <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                  Created At
                </th>
                <th className="text-right py-3 px-4 text-dark-400 font-medium text-sm">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className="border-b border-dark-800/50">
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="w-8 h-8 rounded-full" />
                      <div>
                        <Skeleton className="h-5 w-40 mb-1" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <Skeleton className="h-6 w-24" />
                  </td>
                  <td className="py-3 px-4">
                    <Skeleton className="h-5 w-28" />
                  </td>
                  <td className="py-3 px-4 text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Skeleton className="h-8 w-8 rounded-md" />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );

  const renderRoleManagementSkeleton = () => (
    <div className="mt-8">
      <div className="flex justify-between items-center mb-10">
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-40 mb-2" />
          <Skeleton className="h-5 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="p-4 border border-dark-800 rounded-lg"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-md" />
                    <div>
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-48" />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-9 w-24" />
                    <Skeleton className="h-9 w-24" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (
    isLoadingRoles ||
    isLoadingPermissions ||
    isLoadingUsers ||
    isLoadingTeamMembers
  ) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-5 w-80" />
          </div>
          <Skeleton className="h-10 w-40" />
        </div>
        {renderTeamMembersTableSkeleton()}
        {renderRoleManagementSkeleton()}
      </div>
    );
  }

  // Show error message if team members fetch failed
  if (teamMembersError) {
    toast.error("Failed to load team members");
  }

  const currentUser = usersData?.data;

  // Use team members data if available, otherwise fall back to current user
  const users: User[] =
    teamMembers && teamMembers.length > 0
      ? teamMembers.map((member) => ({
          id: member.id,
          email: member.email,
          first_name: member.first_name,
          last_name: member.last_name,
          roles: [], // We don't have roles in the team members API
          custom_roles: [],
          status: "Active", // Default to Active
          workspace_name: currentUser?.workspace_name || "",
          is_owner: member.is_owner,
          role_name: member.role_name,
          is_custom_role: member.is_custom_role,
          created_at: member.created_at,
        }))
      : [];

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-dark-100 mb-2">
            User Management
          </h1>
          <p className="text-dark-400">
            Manage workspace members and their permissions
          </p>
        </div>

        <Button
          variant="primary"
          leftIcon={<UserPlus size={16} />}
          onClick={() => setNewUserModalOpen(true)}
        >
          Add Team Member
        </Button>
      </div>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            All members with access to your workspace
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingTeamMembers ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-dark-800">
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      User
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Role
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Workspace Owner
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Created At
                    </th>
                    <th className="text-right py-3 px-4 text-dark-400 font-medium text-sm">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {Array.from({ length: 5 }).map((_, index) => (
                    <tr key={index} className="border-b border-dark-800/50">
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-3">
                          <Skeleton className="w-8 h-8 rounded-full" />
                          <div>
                            <Skeleton className="h-5 w-40 mb-1" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Skeleton className="h-6 w-24" />
                      </td>
                      <td className="py-3 px-4">
                        <Skeleton className="h-5 w-28" />
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Skeleton className="h-8 w-8 rounded-md" />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : users && users.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-dark-800">
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      User
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Role
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Workspace Owner
                    </th>
                    <th className="text-left py-3 px-4 text-dark-400 font-medium text-sm">
                      Created At
                    </th>
                    <th className="text-right py-3 px-4 text-dark-400 font-medium text-sm">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr
                      key={user.id}
                      className="border-b border-dark-800/50 hover:bg-dark-900/30 transition-colors"
                    >
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary-500/20 flex items-center justify-center text-primary-400 font-semibold">
                            {(user.first_name || user.email)
                              .charAt(0)
                              .toUpperCase()}
                          </div>
                          <div>
                            {user.first_name || user.last_name ? (
                              <>
                                <p className="text-dark-200">
                                  {user.first_name} {user.last_name}
                                </p>
                                <p className="text-xs text-dark-500">
                                  {user.email}
                                </p>
                              </>
                            ) : (
                              <>
                                <p className="text-dark-200">{user.email}</p>
                                <p className="text-xs text-dark-500">
                                  {user.workspace_name}
                                </p>
                              </>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge
                          variant={
                            user.is_custom_role
                              ? "neutral"
                              : user.role_name === "Admin"
                              ? "primary"
                              : "secondary"
                          }
                          size="sm"
                        >
                          {user.role_name || "No Role"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-dark-300">
                        <Badge
                          variant={user.is_owner ? "primary" : "neutral"}
                          size="sm"
                        >
                          {user.is_owner ? "Yes" : "No"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-dark-300">
                        {user.created_at ? formatDate(user.created_at) : "N/A"}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <button
                            className="p-1 rounded hover:bg-dark-800 text-dark-400 hover:text-dark-200 transition-colors"
                            onClick={() => setEditingUser(user)}
                            title="Edit team member"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            className="p-1 rounded hover:bg-dark-800 text-dark-400 hover:text-yellow-500 transition-colors"
                            onClick={() => handleChangePasswordClick(user)}
                            title="Change password"
                          >
                            <Key size={16} />
                          </button>
                          <button
                            className="p-1 rounded hover:bg-dark-800 text-dark-400 hover:text-red-500 transition-colors"
                            onClick={() => handleDeleteClick(user)}
                            title="Delete team member"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
              <div className="w-16 h-16 rounded-full bg-dark-800 flex items-center justify-center mb-4">
                <UserPlus size={24} className="text-primary-400" />
              </div>
              <h3 className="text-xl font-medium text-dark-200 mb-2">
                No team members found
              </h3>
              <p className="text-dark-400 mb-6 max-w-md">
                Your workspace doesn't have any team members yet. Add your first
                team member to collaborate on your cloud security audits.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Management Section */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-10">
          <div>
            <h1 className="text-2xl font-bold text-dark-100 mb-2">
              Role Management
            </h1>
            <p className="text-dark-400">
              Create and manage custom roles with specific permissions for your
              team members
            </p>
          </div>

          <Button
            onClick={() => setIsCreateModalOpen(true)}
            leftIcon={<Plus size={16} />}
            variant="primary"
          >
            Create Role
          </Button>
        </div>

        {isLoadingRoles || isLoadingPermissions ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="p-4 border border-dark-800 rounded-lg"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-md" />
                    <div>
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-48" />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-9 w-24" />
                    <Skeleton className="h-9 w-24" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <RoleManagement
            isCreateModalOpen={isCreateModalOpen}
            setIsCreateModalOpen={setIsCreateModalOpen}
            customRoles={customRoles}
            permissions={permissions || []}
          />
        )}
      </div>

      {/* New User Modal */}
      <Modal
        isOpen={newUserModalOpen}
        onClose={() => {
          setNewUserModalOpen(false);
          resetAddUserForm();
        }}
        title="Add New Team Member"
      >
        <div className="p-6">
          <p className="text-dark-400 mb-6">
            Invite a new team member to join your workspace. They will receive
            an email with instructions to set up their account.
          </p>

          <form
            onSubmit={handleSubmitCreateUser(onSubmitCreateUser)}
            className="space-y-5 mb-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                placeholder="Enter first name"
                leftIcon={<User size={16} />}
                error={createUserErrors.first_name?.message}
                fullWidth
                {...registerCreateUser("first_name")}
              />

              <Input
                label="Last Name"
                type="text"
                placeholder="Enter last name"
                leftIcon={<User size={16} />}
                error={createUserErrors.last_name?.message}
                fullWidth
                {...registerCreateUser("last_name")}
              />
            </div>

            <Input
              label="Email Address"
              type="email"
              placeholder="Enter user email"
              leftIcon={<Mail size={16} />}
              error={createUserErrors.email?.message}
              fullWidth
              {...registerCreateUser("email")}
            />

            <Input
              label="Password"
              type="password"
              placeholder="Enter password (min. 8 characters)"
              error={createUserErrors.password?.message}
              fullWidth
              {...registerCreateUser("password")}
            />

            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                Role
              </label>
              <select
                className={`w-full bg-dark-800 border ${
                  createUserErrors.role ? "border-error-500" : "border-dark-700"
                } rounded-md px-4 py-2 text-dark-100 focus:outline-none focus:ring-2 ${
                  createUserErrors.role
                    ? "focus:ring-error-500/50 focus:border-error-500"
                    : "focus:ring-primary-500/50 focus:border-primary-500"
                }`}
                onChange={(e) => {
                  const roleId = parseInt(e.target.value);
                  if (!isNaN(roleId)) {
                    const selectedRole = [...roles, ...customRoles].find(
                      (r: Role) => r.id === roleId
                    );
                    if (selectedRole) {
                      // Use setValue with any type to bypass TypeScript checking
                      (setCreateUserValue as any)("role", selectedRole);
                      // Clear the role validation error
                      clearCreateUserErrors("role");
                    }
                  } else {
                    // Use setValue with any type to bypass TypeScript checking
                    (setCreateUserValue as any)("role", null);
                  }
                }}
              >
                <option value="">Select a role</option>
                <optgroup label="Default Roles">
                  {roles.map((role: Role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </optgroup>
                {customRoles.length > 0 && (
                  <optgroup label="Custom Roles">
                    {customRoles.map((role: Role) => (
                      <option key={role.id} value={role.id}>
                        {role.name}
                      </option>
                    ))}
                  </optgroup>
                )}
              </select>
              {createUserErrors.role && (
                <p className="mt-1 text-sm text-error-500">
                  {createUserErrors.role.message}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  setNewUserModalOpen(false);
                  resetAddUserForm();
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={createUserMutation.isPending}
                isLoading={createUserMutation.isPending}
              >
                Add Team Member
              </Button>
            </div>
          </form>
        </div>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        isOpen={!!editingUser}
        onClose={() => setEditingUser(null)}
        title="Edit Team Member"
      >
        {editingUser && (
          <div className="p-6">
            <form
              onSubmit={handleSubmitEditUser(async (data: any) => {
                try {
                  // Ensure we have a valid role
                  if (!data.role || !data.role.id) {
                    toast.error("Please select a valid role");
                    return;
                  }

                  // Check if the selected role is a custom role
                  const isCustomRole = customRoles.some(
                    (role: Role) => role.id === data.role.id
                  );

                  // Show loading toast while updating user
                  const loadingToast = toast.loading("Updating team member...");

                  await updateTeamMemberMutation.mutateAsync({
                    user_id: editingUser.id,
                    first_name: data.first_name,
                    last_name: data.last_name,
                    role_id: data.role.id,
                    is_custom_role: isCustomRole,
                  });

                  // Dismiss loading toast and show success
                  toast.dismiss(loadingToast);
                  toast.success("Team member updated successfully");
                  setEditingUser(null);
                } catch (error) {
                  const errorMessage =
                    error instanceof Error
                      ? error.message
                      : "Failed to update team member";

                  toast.error(errorMessage);
                }
              })}
            >
              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="First Name"
                    type="text"
                    placeholder="Enter first name"
                    leftIcon={<User size={16} />}
                    error={editUserErrors.first_name?.message}
                    fullWidth
                    {...registerEditUser("first_name")}
                  />

                  <Input
                    label="Last Name"
                    type="text"
                    placeholder="Enter last name"
                    leftIcon={<User size={16} />}
                    error={editUserErrors.last_name?.message}
                    fullWidth
                    {...registerEditUser("last_name")}
                  />
                </div>

                <Input
                  label="Email Address"
                  type="email"
                  error={editUserErrors.email?.message}
                  disabled={true}
                  fullWidth
                  {...registerEditUser("email")}
                />

                <div>
                  <label className="block text-sm font-medium text-dark-200 mb-2">
                    Role
                  </label>
                  <select
                    className={`w-full bg-dark-800 border ${
                      editUserErrors.role
                        ? "border-error-500"
                        : "border-dark-700"
                    } rounded-md px-4 py-2 text-dark-100 focus:outline-none focus:ring-2 ${
                      editUserErrors.role
                        ? "focus:ring-error-500/50 focus:border-error-500"
                        : "focus:ring-primary-500/50 focus:border-primary-500"
                    }`}
                    value={getCurrentRoleId(editingUser)}
                    onChange={(e) => {
                      const roleId = parseInt(e.target.value);
                      if (!isNaN(roleId)) {
                        const selectedRole = [...roles, ...customRoles].find(
                          (r: Role) => r.id === roleId
                        );
                        if (selectedRole) {
                          // Use setValue with any type to bypass TypeScript checking
                          (setEditUserValue as any)("role", selectedRole);
                          // Clear the role validation error
                          clearEditUserErrors("role");
                        }
                      } else {
                        // Use setValue with any type to bypass TypeScript checking
                        (setEditUserValue as any)("role", null);
                      }
                    }}
                    disabled={editingUser.id === 1} // Can't change admin role
                  >
                    <option value="">Select a role</option>
                    <optgroup label="Default Roles">
                      {roles.map((role: Role) => (
                        <option key={role.id} value={role.id}>
                          {role.name}
                        </option>
                      ))}
                    </optgroup>
                    {customRoles.length > 0 && (
                      <optgroup label="Custom Roles">
                        {customRoles.map((role: Role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>
                  {editUserErrors.role && (
                    <p className="mt-1 text-sm text-error-500">
                      {editUserErrors.role.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingUser(null)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={updateTeamMemberMutation.isPending}
                  disabled={updateTeamMemberMutation.isPending}
                >
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setUserToDelete(null);
        }}
        onConfirm={handleDeleteUser}
        userName={userToDelete?.email || ""}
        isLoading={deleteUserMutation.isPending}
      />

      {/* Change Password Modal */}
      <Modal
        isOpen={changePasswordModalOpen}
        onClose={() => {
          setChangePasswordModalOpen(false);
          setUserToChangePassword(null);
          resetChangePasswordForm();
        }}
        title="Change Password"
      >
        {userToChangePassword && (
          <div className="p-6">
            <p className="text-dark-400 mb-6">
              Change password for{" "}
              <span className="text-dark-200 font-medium">
                {userToChangePassword.first_name &&
                userToChangePassword.last_name
                  ? `${userToChangePassword.first_name} ${userToChangePassword.last_name}`
                  : userToChangePassword.email}
              </span>
            </p>

            <form
              onSubmit={handleSubmitChangePassword(handleChangePassword)}
              className="space-y-5"
            >
              <Input
                label="New Password"
                type="password"
                placeholder="Enter new password (min. 8 characters)"
                leftIcon={<Key size={16} />}
                error={changePasswordErrors.password?.message}
                fullWidth
                {...registerChangePassword("password")}
              />

              <Input
                label="Confirm New Password"
                type="password"
                placeholder="Confirm new password"
                leftIcon={<Key size={16} />}
                error={changePasswordErrors.confirm_password?.message}
                fullWidth
                {...registerChangePassword("confirm_password")}
              />

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setChangePasswordModalOpen(false);
                    setUserToChangePassword(null);
                    resetChangePasswordForm();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={changePasswordMutation.isPending}
                  disabled={changePasswordMutation.isPending}
                  leftIcon={<Key size={16} />}
                >
                  Change Password
                </Button>
              </div>
            </form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UserManagement;

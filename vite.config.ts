import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const isProduction = command === 'build';

  return {
    plugins: [
      react({
      }),
    ],

    // Development server configuration
    server: {
      host: '0.0.0.0',
      port: 5173,
      strictPort: true,
      // HTTPS is disabled by default, no need to explicitly set to false
    },

    // Preview server configuration (for production builds)
    preview: {
      port: 4173,
      strictPort: true,
      host: '0.0.0.0',
    },

    // Build configuration
    build: {
      // Output directory
      outDir: 'dist',

      // Generate source maps for production debugging
      sourcemap: isProduction ? 'hidden' : true,

      // Minification
      minify: isProduction ? 'esbuild' : false,

      // Target modern browsers for smaller bundles
      target: ['es2020', 'edge88', 'firefox78', 'chrome87', 'safari14'],

      // Chunk size warning limit (500kb)
      chunkSizeWarningLimit: 500,

      // Rollup options for advanced bundling
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            // Vendor chunks
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'ui-vendor': ['framer-motion', 'lucide-react'],
            'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],

            // Utility chunks
            'utils': ['axios', 'clsx', 'tailwind-merge'],
          },

          // Asset file naming
          assetFileNames: (assetInfo) => {
            const fileName = assetInfo.names?.[0] || '';
            const info = fileName.split('.') || [];
            const extType = info[info.length - 1];

            if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(fileName)) {
              return `assets/images/[name]-[hash][extname]`;
            }
            if (/\.(woff2?|eot|ttf|otf)$/i.test(fileName)) {
              return `assets/fonts/[name]-[hash][extname]`;
            }
            if (extType === 'css') {
              return `assets/css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          },

          // Chunk file naming
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
        },
      },

      // Enable/disable CSS code splitting
      cssCodeSplit: true,

      // Emit manifest for deployment tools
      manifest: isProduction,

      // Clean output directory before build
      emptyOutDir: true,
    },

    // Dependency optimization
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        'axios',
        'clsx',
        'tailwind-merge',
      ],
      exclude: ['lucide-react'],
    },

    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@utils': resolve(__dirname, './src/utils'),
        '@hooks': resolve(__dirname, './src/hooks'),
        '@assets': resolve(__dirname, './src/assets'),
        '@stores': resolve(__dirname, './src/stores'),
        '@pages': resolve(__dirname, './src/pages'),
        '@types': resolve(__dirname, './src/types'),
        '@services': resolve(__dirname, './src/services'),
        '@lib': resolve(__dirname, './src/lib'),
        '@schemas': resolve(__dirname, './src/schemas'),
        '@contexts': resolve(__dirname, './src/contexts'),
        '@layouts': resolve(__dirname, './src/layouts'),
      },
    },

    // Environment variables
    define: {
      // Expose environment variables to the client
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __COMMIT_HASH__: JSON.stringify(process.env.VITE_COMMIT_HASH || 'unknown'),
    },

    // CSS configuration
    css: {
      // PostCSS configuration is handled by postcss.config.js
      devSourcemap: !isProduction,

      // CSS modules configuration
      modules: {
        localsConvention: 'camelCase',
      },
    },

    // ESBuild configuration
    esbuild: {
      // Remove console.log in production
      drop: isProduction ? ['console', 'debugger'] : [],

      // Legal comments handling
      legalComments: 'none',
    },
  };
});

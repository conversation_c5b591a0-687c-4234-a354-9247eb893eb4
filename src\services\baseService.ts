import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { refreshToken } from './tokenService';

// Extend the InternalAxiosRequestConfig type to include our custom metadata property
declare module 'axios' {
    export interface InternalAxiosRequestConfig {
        metadata?: {
            startTime: number;
            [key: string]: any;
        };
    }
}

// Application configuration
const APP_ENV = import.meta.env.VITE_APP_ENV || 'development';
const IS_PRODUCTION = APP_ENV === 'production';
const IS_DEVELOPMENT = APP_ENV === 'development';

// Environment-based configuration
const API_URL = IS_PRODUCTION
    ? import.meta.env.VITE_API_BASE_URL
    : import.meta.env.VITE_BACKEND_LOCAL_BASE_URL || 'http://localhost:8000';
const DEFAULT_TIMEOUT = IS_PRODUCTION
    ? parseInt(import.meta.env.VITE_API_TIMEOUT || '30000', 10)
    : parseInt(import.meta.env.VITE_API_TIMEOUT || '60000', 10);
const MAX_RETRIES = IS_PRODUCTION
    ? parseInt(import.meta.env.VITE_API_MAX_RETRIES || '2', 10)
    : parseInt(import.meta.env.VITE_API_MAX_RETRIES || '3', 10);

// Error types for better error handling
export enum ServiceErrorType {
    INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
    NETWORK_ERROR = 'NETWORK_ERROR',
    TOKEN_EXPIRED = 'TOKEN_EXPIRED',
    REFRESH_FAILED = 'REFRESH_FAILED',
    UNAUTHORIZED = 'UNAUTHORIZED',
    SERVER_ERROR = 'SERVER_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    CONFLICT_ERROR = 'CONFLICT_ERROR',
    RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR'
}

export class ServiceError extends Error {
    type: ServiceErrorType;
    originalError?: any;
    statusCode?: number;

    constructor(type: ServiceErrorType, message: string, originalError?: any, statusCode?: number) {
        super(message);
        this.type = type;
        this.originalError = originalError;
        this.statusCode = statusCode;
        this.name = 'ServiceError';
    }
}

// Token management functions
export const getAccessToken = (): string | null => {
    return localStorage.getItem('access_token');
};

export const getRefreshToken = (): string | null => {
    return localStorage.getItem('refresh_token');
};

export const setTokens = (accessToken: string, refreshToken: string): void => {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
};

export const clearTokens = (): void => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_email');
    localStorage.removeItem('workspace_name');
};

// Parse API errors to get the appropriate error type and message
export const parseApiError = (error: any): ServiceError => {
    if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;

        // Network errors
        if (!axiosError.response) {
            return new ServiceError(
                ServiceErrorType.NETWORK_ERROR,
                IS_PRODUCTION
                    ? 'Unable to connect to the server. Please try again later.'
                    : 'Unable to connect to the server. Please check your internet connection.',
                error
            );
        }

        // HTTP error responses
        const status = axiosError.response.status;
        const data = axiosError.response.data as any;

        // Handle specific status codes
        switch (status) {
            case 400:
                return new ServiceError(
                    ServiceErrorType.VALIDATION_ERROR,
                    data?.message || (IS_PRODUCTION
                        ? 'Invalid input. Please try again.'
                        : 'Invalid input. Please check your details and try again.'),
                    error,
                    status
                );

            case 401:
                return new ServiceError(
                    ServiceErrorType.UNAUTHORIZED,
                    data?.message || (IS_PRODUCTION
                        ? 'Please sign in to continue.'
                        : 'Please sign in to continue. Your session may have expired.'),
                    error,
                    status
                );

            case 403:
                return new ServiceError(
                    ServiceErrorType.UNAUTHORIZED,
                    IS_PRODUCTION
                        ? 'You do not have permission to perform this action.'
                        : 'You do not have permission to perform this action. Please check your access rights.',
                    error,
                    status
                );

            case 409:
                return new ServiceError(
                    ServiceErrorType.CONFLICT_ERROR,
                    data?.message || (IS_PRODUCTION
                        ? 'Resource already exists.'
                        : 'Resource already exists. Please check your input.'),
                    error,
                    status
                );

            case 429:
                return new ServiceError(
                    ServiceErrorType.RATE_LIMIT_ERROR,
                    IS_PRODUCTION
                        ? 'Too many requests. Please try again later.'
                        : 'Too many requests. Please wait a moment and try again.',
                    error,
                    status
                );

            case 500:
            case 502:
            case 503:
            case 504:
                return new ServiceError(
                    ServiceErrorType.SERVER_ERROR,
                    IS_PRODUCTION
                        ? 'We\'re experiencing technical difficulties. Please try again later.'
                        : 'Server error occurred. Please try again or contact support if the issue persists.',
                    error,
                    status
                );

            default:
                return new ServiceError(
                    ServiceErrorType.UNKNOWN_ERROR,
                    data?.message || (IS_PRODUCTION
                        ? 'An unexpected error occurred. Please try again.'
                        : 'An unexpected error occurred. Please try again or contact support.'),
                    error,
                    status
                );
        }
    }

    // For non-axios errors
    return new ServiceError(
        ServiceErrorType.UNKNOWN_ERROR,
        error?.message || (IS_PRODUCTION
            ? 'An unexpected error occurred. Please try again.'
            : 'An unexpected error occurred. Please try again or contact support.'),
        error
    );
};

// Create a base axios instance with common configuration
export const createBaseApi = (config?: AxiosRequestConfig): AxiosInstance => {
    const instance = axios.create({
        baseURL: API_URL,
        headers: {
            'Content-Type': 'application/json'
        },
        timeout: DEFAULT_TIMEOUT,
        ...config
    });

    // Add request interceptor for authentication
    instance.interceptors.request.use(
        (config) => {
            const token = getAccessToken();
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }

            // Add request timestamp for performance monitoring
            config.metadata = {
                ...config.metadata,
                startTime: new Date().getTime()
            };

            return config;
        },
        (error) => Promise.reject(error)
    );

    // Add response interceptor for error handling, token refresh, and performance logging
    instance.interceptors.response.use(
        (response: AxiosResponse) => {
            // Calculate request duration for performance monitoring
            const config = response.config;
            if (config.metadata) {
                const endTime = new Date().getTime();
                const duration = endTime - config.metadata.startTime;

                // Log slow requests (over 1 second) to console in development
                if (duration > 1000 && IS_DEVELOPMENT) {
                    console.warn(`Slow API call: ${config.method?.toUpperCase()} ${config.url} took ${duration}ms`);
                }

                // Log performance metrics if enabled or in production
                if (IS_PRODUCTION || import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true') {
                    // You can integrate with performance monitoring services here
                    console.debug(`API Performance: ${config.method?.toUpperCase()} ${config.url} - ${duration}ms`);
                }
            }

            return response;
        },
        async (error) => {
            const originalRequest = error.config;

            // Don't attempt to refresh token for auth endpoints to avoid infinite loops
            const isAuthEndpoint = originalRequest?.url?.includes('/api/login') ||
                originalRequest?.url?.includes('/api/signup') ||
                originalRequest?.url?.includes('/api/refresh-token');

            // Handle 401 Unauthorized errors by refreshing the token
            if (error.response?.status === 401 &&
                !isAuthEndpoint &&
                originalRequest &&
                !originalRequest._isRetryAfterRefresh) {

                try {
                    // Get a new token
                    await refreshToken();

                    // Get the new access token
                    const newToken = getAccessToken();

                    // If we have a new token, retry the request
                    if (newToken) {
                        // Update the Authorization header with the new token
                        originalRequest.headers.Authorization = `Bearer ${newToken}`;

                        // Mark this request as retried to prevent infinite loops
                        originalRequest._isRetryAfterRefresh = true;

                        // Retry the original request with the new token
                        return instance(originalRequest);
                    } else {
                        // If we don't have a new token, something went wrong
                        clearTokens();

                        // Create a more specific error
                        const refreshError = new ServiceError(
                            ServiceErrorType.REFRESH_FAILED,
                            'Token refresh failed. Please sign in again.',
                            error,
                            401
                        );

                        return Promise.reject(refreshError);
                    }
                } catch (refreshError) {
                    // If token refresh fails, clear tokens and reject with a specific error
                    clearTokens();

                    // If the refresh error is already a ServiceError, use it
                    if (refreshError instanceof ServiceError) {
                        return Promise.reject(refreshError);
                    }

                    // Otherwise, create a new ServiceError
                    const tokenError = new ServiceError(
                        ServiceErrorType.REFRESH_FAILED,
                        'Failed to refresh your session. Please sign in again.',
                        refreshError,
                        401
                    );

                    return Promise.reject(tokenError);
                }
            }

            // Implement retry logic for network errors and 5xx errors
            if (
                (error.code === 'ECONNABORTED' ||
                    (error.response && error.response.status >= 500)) &&
                originalRequest &&
                originalRequest._retry !== MAX_RETRIES
            ) {
                originalRequest._retry = (originalRequest._retry || 0) + 1;

                // Exponential backoff: 1s, 2s, 4s, etc.
                const delay = Math.pow(2, originalRequest._retry - 1) * 1000;

                await new Promise(resolve => setTimeout(resolve, delay));

                return instance(originalRequest);
            }

            return Promise.reject(parseApiError(error));
        }
    );

    return instance;
};

// Helper function to create API endpoints
export const createEndpoint = (path: string) => `${API_URL}${path}`;

// Singleton API client instance for the application
export const apiClient = createBaseApi();